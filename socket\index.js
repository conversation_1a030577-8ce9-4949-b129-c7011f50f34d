import WebSocketConfig from '../src/utils/websocket.js';
import WebSocketAuth from '../src/utils/websocket-auth.js';

const socketService = {
    socket: null,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    reconnectInterval: 3000,
    messageHandlers: [],
    authMethod: null, // 记录成功的认证方式
    lastUsername: null, // 记录最后使用的用户名
    lastToken: null, // 记录最后使用的token

    init(username, token = null) {
        if (typeof WebSocket === "undefined") {
            alert("您的浏览器不支持WebSocket");
            return;
        }

        // 保存认证信息用于重连
        this.lastUsername = username;
        this.lastToken = token;

        // 使用新的认证系统
        const wsAuth = new WebSocketAuth();

        wsAuth.attemptConnection(
            username,
            (socket, method) => {
                // 连接成功
                this.socket = socket;
                this.authMethod = method;
                console.log(`✅ WebSocket连接成功，认证方式: ${method}`);

                // 设置事件处理器
                this.socket.onopen = this.open.bind(this);
                this.socket.onerror = this.error.bind(this);
                this.socket.onmessage = this.handleMessage.bind(this);
                this.socket.onclose = this.onClose.bind(this);

                // 触发open事件
                this.open();
            },
            (error) => {
                // 连接失败
                console.error("❌ WebSocket连接失败:", error.message);
                this.handleConnectionError(error);
            }
        );
    },

    // 处理连接错误
    handleConnectionError(error) {
        console.error("WebSocket连接错误详情:", error);

        // 显示用户友好的错误信息
        if (error.message.includes('403')) {
            console.error("🔐 认证失败，请检查登录状态");
        } else if (error.message.includes('超时')) {
            console.error("⏰ 连接超时，请检查网络");
        } else {
            console.error("🌐 网络连接失败，请稍后重试");
        }

        // 触发重连
        this.handleReconnect();
    },

    open() {
        console.log("WebSocket连接成功");
        this.reconnectAttempts = 0; // 重置重连次数
    },

    error(error) {
        console.error("WebSocket连接错误:", error);
        this.reconnect();
    },

    onClose(event) {
        console.log("WebSocket连接关闭:", event.code, event.reason);
        if (event.code !== 1000) { // 非正常关闭
            this.reconnect();
        }
    },

    // 重连机制
    reconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`🔄 WebSocket重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            setTimeout(() => {
                // 使用之前成功的认证方式重连
                this.init(this.lastUsername, this.lastToken);
            }, this.reconnectInterval);
        } else {
            console.error("❌ WebSocket重连失败，已达到最大重连次数");
        }
    },

    // 处理接收到的消息
    handleMessage(event) {
        try {
            let data = event.data;

            // 忽略连接成功消息
            if (data === '连接成功') {
                return;
            }

            // 尝试解析JSON
            try {
                data = JSON.parse(data);
            } catch (e) {
                // 如果不是JSON格式，保持原样
            }

            // 通知所有消息处理器
            this.messageHandlers.forEach(handler => {
                if (typeof handler === 'function') {
                    handler(data);
                }
            });

        } catch (error) {
            console.error("处理WebSocket消息失败:", error);
        }
    },

    // 添加消息处理器
    addMessageHandler(handler) {
        if (typeof handler === 'function') {
            this.messageHandlers.push(handler);
        }
    },

    // 移除消息处理器
    removeMessageHandler(handler) {
        const index = this.messageHandlers.indexOf(handler);
        if (index > -1) {
            this.messageHandlers.splice(index, 1);
        }
    },

    // 兼容旧版本的getMessage方法
    getMessage(callback) {
        if (typeof callback === 'function') {
            this.addMessageHandler(callback);
        }
        return new Promise((resolve) => {
            this.addMessageHandler(resolve);
        });
    },

    send(params) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            const message = typeof params === 'string' ? params : JSON.stringify(params);
            this.socket.send(message);
        } else {
            console.warn("WebSocket未连接，无法发送消息");
        }
    },

    close() {
        if (this.socket) {
            this.socket.close();
            console.log("WebSocket已关闭");
        }
    },

    // 获取连接状态
    getReadyState() {
        return this.socket ? this.socket.readyState : WebSocket.CLOSED;
    },

    // 检查是否已连接
    isConnected() {
        return this.socket && this.socket.readyState === WebSocket.OPEN;
    }
};

// 导出
export default socketService;