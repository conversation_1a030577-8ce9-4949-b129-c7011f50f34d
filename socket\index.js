import WebSocketConfig from '../src/utils/websocket.js';

const socketService = {
    socket: null,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    reconnectInterval: 3000,
    messageHandlers: [],

    init(username) {
        if (typeof WebSocket === "undefined") {
            alert("您的浏览器不支持WebSocket");
            return;
        }

        try {
            // 使用WebSocketConfig获取连接地址
            const params = username ? { username } : {};
            const wsUrl = WebSocketConfig.getWebSocketUrlWithParams('/api/v1/ws', params);

            console.log("WebSocket连接地址:", wsUrl);

            this.socket = new WebSocket(WebSocketConfig.getWebSocketUrl('/api/v1/ws'));
            this.socket.onopen = this.open.bind(this);
            this.socket.onerror = this.error.bind(this);
            this.socket.onmessage = this.handleMessage.bind(this);
            this.socket.onclose = this.onClose.bind(this);
        } catch (error) {
            console.error("WebSocket初始化失败:", error);
        }
    },

    open() {
        console.log("WebSocket连接成功");
        this.reconnectAttempts = 0; // 重置重连次数
    },

    error(error) {
        console.error("WebSocket连接错误:", error);
        this.reconnect();
    },

    onClose(event) {
        console.log("WebSocket连接关闭:", event.code, event.reason);
        if (event.code !== 1000) { // 非正常关闭
            this.reconnect();
        }
    },

    // 重连机制
    reconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`WebSocket重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            setTimeout(() => {
                this.init();
            }, this.reconnectInterval);
        } else {
            console.error("WebSocket重连失败，已达到最大重连次数");
        }
    },

    // 处理接收到的消息
    handleMessage(event) {
        try {
            let data = event.data;

            // 忽略连接成功消息
            if (data === '连接成功') {
                return;
            }

            // 尝试解析JSON
            try {
                data = JSON.parse(data);
            } catch (e) {
                // 如果不是JSON格式，保持原样
            }

            // 通知所有消息处理器
            this.messageHandlers.forEach(handler => {
                if (typeof handler === 'function') {
                    handler(data);
                }
            });

        } catch (error) {
            console.error("处理WebSocket消息失败:", error);
        }
    },

    // 添加消息处理器
    addMessageHandler(handler) {
        if (typeof handler === 'function') {
            this.messageHandlers.push(handler);
        }
    },

    // 移除消息处理器
    removeMessageHandler(handler) {
        const index = this.messageHandlers.indexOf(handler);
        if (index > -1) {
            this.messageHandlers.splice(index, 1);
        }
    },

    // 兼容旧版本的getMessage方法
    getMessage(callback) {
        if (typeof callback === 'function') {
            this.addMessageHandler(callback);
        }
        return new Promise((resolve) => {
            this.addMessageHandler(resolve);
        });
    },

    send(params) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            const message = typeof params === 'string' ? params : JSON.stringify(params);
            this.socket.send(message);
        } else {
            console.warn("WebSocket未连接，无法发送消息");
        }
    },

    close() {
        if (this.socket) {
            this.socket.close();
            console.log("WebSocket已关闭");
        }
    },

    // 获取连接状态
    getReadyState() {
        return this.socket ? this.socket.readyState : WebSocket.CLOSED;
    },

    // 检查是否已连接
    isConnected() {
        return this.socket && this.socket.readyState === WebSocket.OPEN;
    }
};

// 导出
export default socketService;