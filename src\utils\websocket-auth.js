/**
 * WebSocket 认证工具
 * 提供多种WebSocket认证方式的支持
 */

import { getToken } from "@/utils/auth";
import WebSocketConfig from './websocket.js';

class WebSocketAuth {
  constructor() {
    this.authMethods = [
      'subprotocol',  // 子协议方式
      'url_params',   // URL参数方式
      'post_auth',    // 连接后认证
      'basic'         // 基础连接
    ];
    this.currentMethodIndex = 0;
  }
  
  /**
   * 尝试建立认证连接
   * @param {string} username - 用户名或频道名
   * @param {function} onSuccess - 成功回调
   * @param {function} onError - 失败回调
   */
  async attemptConnection(username = 'warning-all', onSuccess, onError) {
    const token = getToken();
    
    if (!token) {
      onError(new Error('未获取到认证token'));
      return;
    }
    
    console.log('🔐 开始WebSocket认证连接...');
    
    for (let i = 0; i < this.authMethods.length; i++) {
      const method = this.authMethods[i];
      console.log(`🔄 尝试认证方式 ${i + 1}/${this.authMethods.length}: ${method}`);
      
      try {
        const result = await this.tryAuthMethod(method, username, token);
        if (result.success) {
          console.log(`✅ 认证成功，使用方式: ${method}`);
          onSuccess(result.socket, method);
          return;
        }
      } catch (error) {
        console.log(`❌ 认证方式 ${method} 失败:`, error.message);
      }
    }
    
    onError(new Error('所有认证方式都失败了'));
  }
  
  /**
   * 尝试特定的认证方式
   */
  async tryAuthMethod(method, username, token) {
    switch (method) {
      case 'subprotocol':
        return await this.trySubprotocolAuth(username, token);
      case 'url_params':
        return await this.tryUrlParamsAuth(username, token);
      case 'post_auth':
        return await this.tryPostAuth(username, token);
      case 'basic':
        return await this.tryBasicAuth(username);
      default:
        throw new Error(`未知的认证方式: ${method}`);
    }
  }
  
  /**
   * 子协议认证方式
   * 模拟HTTP Header的效果
   */
  async trySubprotocolAuth(username, token) {
    return new Promise((resolve, reject) => {
      const wsUrl = WebSocketConfig.getWebSocketUrl('/api/v1/ws');
      
      // 使用子协议传递认证信息
      const protocols = [
        `authorization.Bearer.${token}`,  // 模拟 Authorization: Bearer token
        `username.${username}`            // 传递用户名
      ];
      
      console.log('🔗 子协议连接:', wsUrl);
      console.log('📋 子协议:', protocols);
      
      const ws = new WebSocket(wsUrl, protocols);
      
      const timeout = setTimeout(() => {
        ws.close();
        reject(new Error('连接超时'));
      }, 5000);
      
      ws.onopen = () => {
        clearTimeout(timeout);
        console.log('✅ 子协议连接成功');
        resolve({ success: true, socket: ws });
      };
      
      ws.onerror = (error) => {
        clearTimeout(timeout);
        reject(new Error('子协议连接失败'));
      };
      
      ws.onclose = (event) => {
        clearTimeout(timeout);
        if (event.code === 403) {
          reject(new Error('403 Forbidden - 子协议认证失败'));
        } else if (event.code !== 1000) {
          reject(new Error(`连接关闭: ${event.code} ${event.reason}`));
        }
      };
    });
  }
  
  /**
   * URL参数认证方式
   */
  async tryUrlParamsAuth(username, token) {
    return new Promise((resolve, reject) => {
      const params = {
        token: token,
        username: username
      };
      
      const wsUrl = WebSocketConfig.getWebSocketUrlWithParams('/api/v1/ws', params);
      console.log('🔗 URL参数连接:', wsUrl);
      
      const ws = new WebSocket(wsUrl);
      
      const timeout = setTimeout(() => {
        ws.close();
        reject(new Error('连接超时'));
      }, 5000);
      
      ws.onopen = () => {
        clearTimeout(timeout);
        console.log('✅ URL参数连接成功');
        resolve({ success: true, socket: ws });
      };
      
      ws.onerror = (error) => {
        clearTimeout(timeout);
        reject(new Error('URL参数连接失败'));
      };
      
      ws.onclose = (event) => {
        clearTimeout(timeout);
        if (event.code === 403) {
          reject(new Error('403 Forbidden - URL参数认证失败'));
        } else if (event.code !== 1000) {
          reject(new Error(`连接关闭: ${event.code} ${event.reason}`));
        }
      };
    });
  }
  
  /**
   * 连接后认证方式
   */
  async tryPostAuth(username, token) {
    return new Promise((resolve, reject) => {
      const wsUrl = WebSocketConfig.getWebSocketUrl('/api/v1/ws');
      console.log('🔗 连接后认证:', wsUrl);
      
      const ws = new WebSocket(wsUrl);
      let authSent = false;
      
      const timeout = setTimeout(() => {
        ws.close();
        reject(new Error('认证超时'));
      }, 10000);
      
      ws.onopen = () => {
        console.log('📤 发送认证消息...');
        
        // 发送认证消息
        const authMessage = {
          type: 'auth',
          token: token,
          username: username
        };
        
        ws.send(JSON.stringify(authMessage));
        authSent = true;
      };
      
      ws.onmessage = (event) => {
        console.log('📨 收到消息:', event.data);
        
        // 检查认证响应
        if (event.data === '连接成功' || 
            event.data.includes('成功') || 
            event.data.includes('authenticated')) {
          clearTimeout(timeout);
          console.log('✅ 连接后认证成功');
          resolve({ success: true, socket: ws });
        }
      };
      
      ws.onerror = (error) => {
        clearTimeout(timeout);
        reject(new Error('连接后认证失败'));
      };
      
      ws.onclose = (event) => {
        clearTimeout(timeout);
        if (event.code === 403) {
          reject(new Error('403 Forbidden - 连接后认证失败'));
        } else if (authSent && event.code !== 1000) {
          reject(new Error('认证消息发送后连接关闭'));
        } else if (event.code !== 1000) {
          reject(new Error(`连接关闭: ${event.code}`));
        }
      };
    });
  }
  
  /**
   * 基础连接方式（无认证）
   */
  async tryBasicAuth(username) {
    return new Promise((resolve, reject) => {
      const params = username ? { username } : {};
      const wsUrl = WebSocketConfig.getWebSocketUrlWithParams('/api/v1/ws', params);
      console.log('🔗 基础连接:', wsUrl);
      
      const ws = new WebSocket(wsUrl);
      
      const timeout = setTimeout(() => {
        ws.close();
        reject(new Error('连接超时'));
      }, 5000);
      
      ws.onopen = () => {
        clearTimeout(timeout);
        console.log('✅ 基础连接成功');
        resolve({ success: true, socket: ws });
      };
      
      ws.onerror = (error) => {
        clearTimeout(timeout);
        reject(new Error('基础连接失败'));
      };
      
      ws.onclose = (event) => {
        clearTimeout(timeout);
        if (event.code !== 1000) {
          reject(new Error(`连接关闭: ${event.code} ${event.reason}`));
        }
      };
    });
  }
  
  /**
   * 获取建议的认证方式
   */
  getAuthAdvice() {
    return {
      subprotocol: {
        name: '子协议认证',
        description: '使用WebSocket子协议传递认证信息，类似HTTP Header',
        pros: ['最接近HTTP Header方式', '安全性较高'],
        cons: ['服务器需要支持子协议解析']
      },
      url_params: {
        name: 'URL参数认证',
        description: '在WebSocket URL中传递token参数',
        pros: ['实现简单', '兼容性好'],
        cons: ['token可能在日志中暴露']
      },
      post_auth: {
        name: '连接后认证',
        description: '先建立连接，然后发送认证消息',
        pros: ['灵活性高', '可以处理复杂认证流程'],
        cons: ['需要额外的消息处理逻辑']
      },
      basic: {
        name: '基础连接',
        description: '不进行认证的基础连接',
        pros: ['最简单'],
        cons: ['无安全保障']
      }
    };
  }
}

export default WebSocketAuth;
