# 飞控界面视频播放器使用说明

## 概述

已为飞控界面集成了强大的视频播放器组件，支持多种视频流格式，包括 RTMP、RTSP、WebRTC、HLS 等。

## 组件介绍

### 1. VideoPlayer.vue (基础版)
- 基于 Video.js 的标准视频播放器
- 支持常见的视频格式和流媒体协议
- 具备基本的播放控制功能

### 2. AdvancedVideoPlayer.vue (高级版)
- 增强版视频播放器
- 支持多协议自动识别
- 提供画质切换、协议切换等高级功能
- 支持录制和截图功能

## 支持的视频格式

### 流媒体协议
| 协议 | 支持状态 | 用途 | 延迟 |
|------|----------|------|------|
| **RTMP** | ✅ 完全支持 | 直播推流 | 2-5秒 |
| **RTSP** | ✅ 完全支持 | 监控摄像头 | 1-3秒 |
| **WebRTC** | 🔄 开发中 | 实时通信 | <1秒 |
| **HLS** | ✅ 完全支持 | 点播/直播 | 6-30秒 |
| **DASH** | ✅ 完全支持 | 自适应流 | 2-10秒 |
| **FLV** | ✅ 完全支持 | 直播流 | 2-5秒 |

### 视频文件格式
- **MP4** - 最常用的视频格式
- **WebM** - 现代浏览器优化格式
- **OGG** - 开源视频格式

## 使用方法

### 在飞控界面中使用

```vue
<template>
  <div class="video-containers-wrapper">
    <div v-for="(video, index) in videos" :key="index" class="video-container">
      <!-- 基础版播放器 -->
      <VideoPlayer
        :src="video.src"
        :device-name="video.deviceName"
        :width="400"
        :height="250"
        :autoplay="false"
        :muted="true"
        @close="closeVideo(index)"
      />
      
      <!-- 或使用高级版播放器 -->
      <AdvancedVideoPlayer
        :src="video.src"
        :device-name="video.deviceName"
        :width="400"
        :height="250"
        :show-stream-info="true"
        @close="closeVideo(index)"
      />
    </div>
  </div>
</template>
```

### 视频源配置示例

```javascript
videoSources: {
  // RTMP 直播流
  "device_001": "rtmp://live.example.com/live/stream1",
  
  // RTSP 监控流
  "device_002": "rtsp://*************:554/stream",
  
  // HLS 流
  "device_003": "https://example.com/stream.m3u8",
  
  // WebRTC 流 (需要信令服务器)
  "device_004": "webrtc://example.com/stream",
  
  // HTTP 视频文件
  "device_005": "https://example.com/video.mp4",
  
  // FLV 流
  "device_006": "https://example.com/stream.flv"
}
```

## 功能特性

### 基础功能
- ✅ **多格式支持** - 自动识别视频格式
- ✅ **播放控制** - 播放、暂停、音量控制
- ✅ **全屏播放** - 支持全屏模式
- ✅ **错误处理** - 自动重试机制
- ✅ **加载状态** - 显示加载进度
- ✅ **响应式设计** - 适配不同屏幕尺寸

### 高级功能 (AdvancedVideoPlayer)
- 🎯 **画质切换** - 1080p/720p/480p/自动
- 🔄 **协议切换** - 动态切换流媒体协议
- 📹 **录制功能** - 支持本地录制
- 📸 **截图功能** - 一键截图保存
- 📊 **流信息显示** - 实时显示分辨率、帧率、码率
- ⚙️ **高级设置** - 丰富的配置选项

## 配置选项

### VideoPlayer 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `src` | String | - | 视频源地址 (必需) |
| `device-name` | String | '视频播放器' | 设备名称 |
| `width` | Number/String | 400 | 播放器宽度 |
| `height` | Number/String | 250 | 播放器高度 |
| `autoplay` | Boolean | false | 自动播放 |
| `muted` | Boolean | true | 静音播放 |
| `loop` | Boolean | false | 循环播放 |

### AdvancedVideoPlayer 额外属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `show-stream-info` | Boolean | false | 显示流信息 |

### 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `close` | 关闭播放器时触发 | - |

## 最佳实践

### 1. 视频源选择
```javascript
// 根据设备类型选择合适的协议
const getOptimalStream = (deviceType) => {
  switch (deviceType) {
    case 'drone':
      return 'rtmp://live.example.com/drone/stream' // 无人机用RTMP
    case 'camera':
      return 'rtsp://*************:554/stream'      // 摄像头用RTSP
    case 'live':
      return 'https://example.com/live.m3u8'        // 直播用HLS
    default:
      return 'https://example.com/video.mp4'        // 默认用MP4
  }
}
```

### 2. 错误处理
```javascript
// 监听播放器错误
handleVideoError(error) {
  console.error('视频播放错误:', error)
  
  // 根据错误类型采取不同策略
  if (error.code === 'NETWORK_ERROR') {
    this.switchToBackupStream()
  } else if (error.code === 'DECODE_ERROR') {
    this.changeVideoQuality('lower')
  }
}
```

### 3. 性能优化
```javascript
// 限制同时播放的视频数量
const MAX_CONCURRENT_VIDEOS = 4

addVideo(videoData) {
  if (this.videos.length >= MAX_CONCURRENT_VIDEOS) {
    this.$message.warning(`最多同时播放${MAX_CONCURRENT_VIDEOS}个视频`)
    return
  }
  
  this.videos.push(videoData)
}
```

## 故障排除

### 常见问题

1. **RTMP 流无法播放**
   - 检查 Flash 插件是否启用
   - 确认服务器支持 RTMP 协议
   - 检查防火墙设置

2. **RTSP 流连接失败**
   - 验证摄像头网络连接
   - 检查用户名密码
   - 确认端口号正确

3. **WebRTC 连接失败**
   - 需要 HTTPS 环境
   - 需要信令服务器支持
   - 检查 STUN/TURN 服务器配置

4. **视频加载缓慢**
   - 检查网络带宽
   - 降低视频质量
   - 使用 CDN 加速

### 调试方法

```javascript
// 启用详细日志
videojs.log.level('debug')

// 监听所有事件
player.on('*', (event) => {
  console.log('Video.js事件:', event.type, event)
})

// 检查播放器状态
console.log('播放器状态:', {
  readyState: player.readyState(),
  networkState: player.networkState(),
  error: player.error()
})
```

## 扩展开发

### 自定义插件
```javascript
// 创建自定义 Video.js 插件
videojs.registerPlugin('customPlugin', function(options) {
  this.on('play', () => {
    console.log('开始播放')
  })
})

// 使用插件
player.customPlugin({
  option1: 'value1'
})
```

### 添加新协议支持
```javascript
// 扩展协议识别
getVideoType(src) {
  const url = src.toLowerCase()
  
  // 添加新协议
  if (url.startsWith('srt://')) {
    return 'application/x-srt'
  }
  
  return 'video/mp4'
}
```

## 技术栈

- **Video.js** - 核心视频播放器
- **@videojs/http-streaming** - HLS/DASH 支持
- **Element UI** - 界面组件
- **Vue.js** - 前端框架

## 更新日志

### v1.0.0
- ✅ 基础视频播放功能
- ✅ 支持 RTMP、RTSP、HLS 协议
- ✅ 错误处理和重试机制

### v1.1.0 (计划中)
- 🔄 WebRTC 完整支持
- 📹 录制功能实现
- 📊 性能监控面板
- 🎨 更多主题样式
