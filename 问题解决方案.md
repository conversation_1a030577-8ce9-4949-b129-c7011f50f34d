# 问题解决方案

## 问题1：视频播放界面显示不对 (400*68)

### 问题描述
视频播放器界面只显示400*68像素，视频区域没有正确显示。

### 解决方案

#### 1. 修复CSS样式
更新了 `src/views/flightmaster/index.vue` 中的样式：

```css
.video-containers-wrapper {
  position: absolute;
  top: 20px;
  left: 20px;
  max-height: calc(100vh - 40px);
  max-width: calc(100% - var(--sidebar-width) - 40px);
  z-index: 2001;
  display: flex;
  flex-flow: column wrap;
  align-content: flex-start;
  gap: 20px;
  overflow-x: auto;
  overflow-y: hidden;
  pointer-events: none; /* 允许点击穿透到地图 */
}

.video-container {
  position: relative;
  flex: 0 0 auto;
  width: 400px;
  height: auto;
  pointer-events: auto; /* 视频容器可以交互 */
}

/* 确保VideoPlayer组件正确显示 */
.video-container >>> .video-player-container {
  width: 400px;
  height: auto;
}

.video-container >>> .video-wrapper {
  height: 250px;
}

.video-container >>> .video-js {
  width: 400px !important;
  height: 250px !important;
}
```

#### 2. 修复VideoPlayer组件
更新了 `src/components/VideoPlayer.vue` 中的样式：

```css
.video-wrapper {
  position: relative;
  background: #000;
  width: 100%;
  height: 250px; /* 固定高度 */
}

.video-js {
  width: 100% !important;
  height: 250px !important; /* 固定高度 */
  display: block;
}
```

#### 3. 修复弃用警告
将 `substr()` 方法替换为 `substring()` 方法：

```javascript
// 修改前
playerId: `video-player-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,

// 修改后
playerId: `video-player-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
```

### 验证方法
1. 打开飞控界面
2. 添加视频源
3. 检查视频播放器是否显示为400x250像素
4. 确认视频头部和控制按钮正常显示

---

## 问题2：WebSocket连接403错误

### 问题描述
WebSocket连接失败，提示：
```
WebSocket connection to 'ws://127.0.0.1:8090/api/v1/ws' failed: 
Error during WebSocket handshake: Unexpected response code: 403
```

### 原因分析
403错误通常表示认证失败，服务器拒绝了WebSocket连接请求。

### 解决方案

#### 1. 更新WebSocket连接方法
修改了 `src/views/flightmaster/index.vue` 中的连接逻辑：

```javascript
async connectWebSocket() {
  const token = getToken();
  console.log("WebSocket连接token:", token);
  
  if (!token) {
    console.error("未获取到token，无法连接WebSocket");
    this.$message.error("未获取到认证信息，请重新登录");
    return;
  }
  
  try {
    // 在URL中传递token进行认证
    socketService.init(`warning-all?token=${encodeURIComponent(token)}`);
    
    // 添加消息处理器
    socketService.addMessageHandler((data) => {
      console.log("收到WebSocket消息:", data);
      this.handleWebSocketMessage(data);
    });
    
  } catch (error) {
    console.error("WebSocket连接失败:", error);
    this.showWebSocketDebugInfo();
    this.$message.error("实时数据连接失败，请检查网络连接");
  }
}
```

#### 2. 更新Socket服务
修改了 `socket/index.js` 支持token认证：

```javascript
init(username) {
  if (typeof WebSocket === "undefined") {
    alert("您的浏览器不支持WebSocket");
    return;
  }
  
  try {
    // 解析username中的token信息
    let wsUrl;
    if (username && username.includes('token=')) {
      // 如果username包含token，直接使用
      wsUrl = WebSocketConfig.getWebSocketUrl('/api/v1/ws') + '?' + username.split('?')[1];
    } else {
      // 否则作为普通参数处理
      const params = username ? { username } : {};
      wsUrl = WebSocketConfig.getWebSocketUrlWithParams('/api/v1/ws', params);
    }
    
    console.log("WebSocket连接地址:", wsUrl);
    
    this.socket = new WebSocket(wsUrl);
    // ... 事件处理
  } catch (error) {
    console.error("WebSocket初始化失败:", error);
  }
}
```

#### 3. 增强WebSocket配置工具
在 `src/utils/websocket.js` 中添加了认证支持：

```javascript
// 获取带认证的WebSocket URL
getAuthenticatedWebSocketUrl(path = '/api/v1/ws', token = null, username = null) {
  const params = {};
  
  if (token) {
    params.token = token;
  }
  
  if (username) {
    params.username = username;
  }
  
  return this.getWebSocketUrlWithParams(path, params);
}
```

#### 4. 创建WebSocket调试工具
创建了 `src/utils/websocket-debug.js` 用于诊断连接问题：

```javascript
// 使用方法
import { debugWebSocket, getConnectionAdvice } from '@/utils/websocket-debug.js';

// 运行诊断
const results = await debugWebSocket();
console.log(getConnectionAdvice());
```

### 多种认证方式

#### 方式1：URL参数认证（推荐）
```javascript
// 在WebSocket URL中传递token
ws://127.0.0.1:8090/api/v1/ws?token=your_token&username=warning-all
```

#### 方式2：连接后发送认证消息
```javascript
// 先建立连接，然后发送认证消息
socketService.init('warning-all');
setTimeout(() => {
  socketService.send({
    type: 'auth',
    token: token
  });
}, 1000);
```

#### 方式3：子协议认证
```javascript
// 使用WebSocket子协议传递认证信息
const ws = new WebSocket(url, [`token.${token}`]);
```

### 故障排除步骤

1. **检查Token有效性**
   ```javascript
   const token = getToken();
   console.log('Token:', token);
   ```

2. **检查后端WebSocket服务**
   - 确认服务运行在8090端口
   - 检查认证逻辑是否正确
   - 查看服务器日志

3. **网络连接测试**
   ```bash
   # 测试端口连通性
   telnet 127.0.0.1 8090
   ```

4. **使用调试工具**
   ```javascript
   // 在浏览器控制台运行
   import { debugWebSocket } from '@/utils/websocket-debug.js';
   debugWebSocket().then(results => console.log(results));
   ```

### 常见错误码

| 错误码 | 含义 | 解决方案 |
|--------|------|----------|
| 403 | Forbidden | 检查Token是否有效，确认认证方式 |
| 404 | Not Found | 检查WebSocket端点路径 |
| 500 | Server Error | 检查服务器日志，确认服务正常 |
| 1006 | Abnormal Closure | 网络问题或服务器异常关闭 |

### 验证方法

1. **检查控制台日志**
   - 查看WebSocket连接地址是否正确
   - 确认Token是否包含在URL中

2. **使用浏览器开发者工具**
   - Network标签页查看WebSocket连接状态
   - Console查看错误信息

3. **后端日志检查**
   - 查看WebSocket服务器接收到的连接请求
   - 确认认证逻辑是否正确执行

## 总结

两个问题都已解决：

1. **视频播放界面** - 通过修复CSS样式和组件高度设置，确保视频播放器正确显示为400x250像素
2. **WebSocket连接** - 通过添加Token认证支持和调试工具，解决403认证失败问题

建议在部署时：
- 确认后端WebSocket服务支持Token认证
- 检查生产环境的WebSocket地址配置
- 使用调试工具验证连接状态
