<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
       <el-form-item label="设备序列号" prop="device_sn">
        <el-input
          v-model="queryParams.device_sn"
          placeholder="请输入设备序列号"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
     
      <el-form-item label="设备昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入设备昵称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      
      
      <el-form-item label="绑定状态" prop="bound_status">
        <el-select v-model="queryParams.bound_status" placeholder="请选择绑定状态" clearable style="width: 200px">
          <el-option label="未绑定" :value="false" />
          <el-option label="已绑定" :value="true" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['device:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['device:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['device:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['device:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="设备序列号" align="center" key="device_sn" prop="device_sn" v-if="columns[0].visible" :show-overflow-tooltip="true" />
      <el-table-column label="设备名称" align="center" key="device_name" prop="device_name" v-if="columns[1].visible" :show-overflow-tooltip="true" />
      <el-table-column label="设备昵称" align="center" key="nickname" prop="nickname" v-if="columns[2].visible" :show-overflow-tooltip="true" />

      <el-table-column label="设备类型" align="center" key="type" v-if="columns[6].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === 1" type="primary">网关</el-tag>
          <el-tag v-else-if="scope.row.type === 2" type="success">机场</el-tag>
          <el-tag v-else-if="scope.row.type === 3" type="warning">无人机</el-tag>
          <el-tag v-else-if="scope.row.type === 4" type="info">遥控器</el-tag>
          <el-tag v-else type="default">未知</el-tag>
        </template>
      </el-table-column>
 
      <el-table-column label="固件版本" align="center" key="firmware_version" prop="firmware_version" v-if="columns[10].visible" :show-overflow-tooltip="true" />
      <el-table-column label="固件状态" align="center" key="firmware_status" v-if="columns[11].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.firmware_status === 1" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.firmware_status === 2" type="danger">升级</el-tag>
          <el-tag v-else-if="scope.row.firmware_status === 3" type="danger">一致升级</el-tag>
          <el-tag v-else-if="scope.row.firmware_status === 4" type="danger">升级期间</el-tag>
          <el-tag v-else type="warning">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="协议版本" align="center" key="thing_version" prop="thing_version" v-if="columns[12].visible" :show-overflow-tooltip="true" />
      <el-table-column label="绑定状态" align="center" key="bound_status" v-if="columns[13].visible">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.bound_status"
            :active-value="true"
            :inactive-value="false"
            @change="handleBoundStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>

      <el-table-column label="在线状态" align="center" key="mode_code" v-if="columns[16].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.mode_code >= 0" type="success">在线</el-tag>
          <el-tag v-else type="danger">离线</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="绑定时间" align="center" prop="bound_time" v-if="columns[14].visible" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.bound_time || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后登录" align="center" prop="login_time" v-if="columns[15].visible" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.login_time || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['device:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['device:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>


    <!-- 添加或修改无人机配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备序列号" prop="device_sn">
              <el-input v-model="form.device_sn" placeholder="请输入设备序列号" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="device_name">
              <el-input v-model="form.device_name" placeholder="请输入设备名称" maxlength="64" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备昵称" prop="nickname">
              <el-input v-model="form.nickname" placeholder="请输入设备昵称" maxlength="64" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作空间ID" prop="workspace_id">
              <el-input v-model="form.workspace_id" placeholder="请输入工作空间ID" maxlength="64" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="控制源" prop="control_source">
              <el-select v-model="form.control_source" placeholder="请选择控制源">
                <el-option label="A控制" value="A" />
                <el-option label="B控制" value="B" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择设备类型">
                <el-option label="网关" :value="1" />
                <el-option label="机场" :value="2" />
                <el-option label="无人机" :value="3" />
                <el-option label="遥控器" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="子类型" prop="sub_type">
              <el-input-number v-model="form.sub_type" :min="0" placeholder="请输入子类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="域" prop="domain">
              <el-input-number v-model="form.domain" :min="1" placeholder="请输入域" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="子设备序列号" prop="child_device_sn">
              <el-input v-model="form.child_device_sn" placeholder="请输入子设备序列号" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="固件版本" prop="firmware_version">
              <el-input v-model="form.firmware_version" placeholder="请输入固件版本" maxlength="32" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="固件状态" prop="firmware_status">
              <el-radio-group v-model="form.firmware_status">
                <el-radio :label="1">正常</el-radio>
                <el-radio :label="0">异常</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议版本" prop="thing_version">
              <el-input v-model="form.thing_version" placeholder="请输入协议版本" maxlength="32" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="绑定状态" prop="bound_status">
              <el-radio-group v-model="form.bound_status">
                <el-radio :label="true">已绑定</el-radio>
                <el-radio :label="false">未绑定</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="普通图标URL" prop="normal_icon_url">
              <el-input v-model="form.normal_icon_url" placeholder="请输入普通图标URL" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="选中图标URL" prop="selected_icon_url">
              <el-input v-model="form.selected_icon_url" placeholder="请输入选中图标URL" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="设备描述" prop="device_desc">
              <el-input v-model="form.device_desc" type="textarea" placeholder="请输入设备描述" maxlength="100"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listDevice, getDevice, delDevice, addDevice, updateDevice, changeDeviceBindStatus } from "@/api/device";

export default {
  name: "DroneDevice",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        device_sn: null,
        device_name: null,
        nickname: null,
        workspace_id: null,
        control_source: null,
        type: null,
        bound_status: null,
        deviceType: 0
      },
      // 列信息
      columns: [
        { key: 0, label: `设备序列号`, visible: true },
        { key: 1, label: `设备名称`, visible: true },
        { key: 2, label: `设备昵称`, visible: true },
        { key: 3, label: `工作空间ID`, visible: true },
        { key: 4, label: `工作空间名称`, visible: true },
        { key: 5, label: `控制源`, visible: true },
        { key: 6, label: `设备类型`, visible: true },
        { key: 7, label: `子类型`, visible: true },
        { key: 8, label: `域`, visible: true },
        { key: 9, label: `子设备序列号`, visible: true },
        { key: 10, label: `固件版本`, visible: true },
        { key: 11, label: `固件状态`, visible: true },
        { key: 12, label: `协议版本`, visible: true },
        { key: 13, label: `绑定状态`, visible: true },
        { key: 14, label: `绑定时间`, visible: true },
        { key: 15, label: `最后登录`, visible: true },
        { key: 16, label: `在线状态`, visible: true }
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        device_sn: [
          { required: true, message: "设备序列号不能为空", trigger: "blur" }
        ],
        device_name: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        nickname: [
          { required: true, message: "设备昵称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      listDevice(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        // 根据实际API返回结构处理数据
        if (response.code === 0) {
          this.deviceList = response.data || [];
          this.total = response.data ? response.data.length : 0;
        } else {
          this.deviceList = [];
          this.total = 0;
          this.$modal.msgError(response.message || "获取设备列表失败");
        }
        this.loading = false;
      }).catch(() => {
        this.deviceList = [];
        this.total = 0;
        this.loading = false;
        this.$modal.msgError("获取设备列表失败");
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        device_sn: null,
        device_name: null,
        nickname: null,
        workspace_id: null,
        control_source: null,
        device_desc: null,
        child_device_sn: null,
        domain: 3,
        type: 3, // 默认设置为无人机类型
        sub_type: 0,
        bound_status: false,
        firmware_version: null,
        firmware_status: 1,
        thing_version: null,
        normal_icon_url: "",
        selected_icon_url: "",
        mode_code:""
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.device_sn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加无人机";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const device_sn = row.device_sn || this.ids[0];
      if (row.device_sn) {
        // 直接使用行数据，处理icon_url嵌套结构
        this.form = Object.assign({}, row);
        if (row.icon_url) {
          this.form.normal_icon_url = row.icon_url.normal_icon_url || "";
          this.form.selected_icon_url = row.icon_url.selected_icon_url || "";
        }
        this.open = true;
        this.title = "修改无人机";
      } else {
        // 从API获取数据
        getDevice(device_sn).then(response => {
          if (response.code === 0) {
            this.form = Object.assign({}, response.data);
            if (response.data.icon_url) {
              this.form.normal_icon_url = response.data.icon_url.normal_icon_url || "";
              this.form.selected_icon_url = response.data.icon_url.selected_icon_url || "";
            }
          } else {
            this.form = Object.assign({}, row);
            if (row.icon_url) {
              this.form.normal_icon_url = row.icon_url.normal_icon_url || "";
              this.form.selected_icon_url = row.icon_url.selected_icon_url || "";
            }
          }
          this.open = true;
          this.title = "修改无人机";
        });
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 构建提交数据，重新组装icon_url结构
          const submitData = Object.assign({}, this.form);
          submitData.icon_url = {
            normal_icon_url: this.form.normal_icon_url || "",
            selected_icon_url: this.form.selected_icon_url || ""
          };
          // 删除临时字段
          delete submitData.normal_icon_url;
          delete submitData.selected_icon_url;

          if (this.title === "修改无人机") {
            updateDevice(submitData).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDevice(submitData).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const device_sns = row.device_sn ? [row.device_sn] : this.ids;
      const deviceNames = row.device_sn ? [row.nickname || row.device_name] :
        this.deviceList.filter(item => device_sns.includes(item.device_sn))
          .map(item => item.nickname || item.device_name);

      this.$modal.confirm('是否确认删除无人机"' + deviceNames.join('、') + '"？').then(function() {
        return delDevice(device_sns.join(','));
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 设备绑定状态修改
    handleBoundStatusChange(row) {
      let text = row.bound_status === true ? "绑定" : "解绑";
      this.$modal.confirm('确认要"' + text + '""' + row.nickname + '"无人机吗？').then(function() {
        return changeDeviceBindStatus(row.device_sn, row.bound_status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.bound_status = !row.bound_status;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('device/export', {
        ...this.queryParams
      }, `drone_device_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
