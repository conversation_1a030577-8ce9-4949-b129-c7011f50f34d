# 设备查询界面

基于您提供的设备表SQL结构，我已经创建了一个完整的设备查询界面。

## 功能特性

### 1. 搜索功能
- **设备序列号**：支持模糊搜索设备序列号
- **设备名称**：支持模糊搜索设备名称
- **用户ID**：根据绑定用户ID筛选设备
- **设备昵称**：支持模糊搜索设备昵称
- **工作空间ID**：根据工作空间筛选设备
- **设备类型**：下拉选择设备类型（坞站、无人机、遥控器等）
- **绑定状态**：筛选已绑定/未绑定的设备
- **创建时间**：支持时间范围筛选

### 2. 设备列表展示
- **基本信息**：设备ID、序列号、名称、用户ID、昵称、工作空间ID
- **设备属性**：设备类型、子类型、固件版本、兼容状态
- **状态管理**：绑定状态（支持一键切换）
- **时间信息**：绑定时间、最后登录时间、创建时间

### 3. 操作功能
- **新增设备**：添加新的设备记录
- **修改设备**：编辑现有设备信息
- **删除设备**：删除设备记录（支持批量删除）
- **导出数据**：导出设备列表到Excel
- **状态切换**：一键切换设备绑定状态

### 4. 界面特性
- **响应式设计**：适配不同屏幕尺寸
- **分页显示**：支持大量数据的分页展示
- **列显示控制**：可自定义显示的列
- **搜索条件折叠**：可隐藏/显示搜索表单
- **数据加载状态**：显示加载动画

## 文件结构

```
src/
├── api/
│   └── device.js              # 设备相关API接口
├── views/
│   └── device/
│       ├── index.vue          # 设备管理主页面
│       └── demo.vue           # 演示页面（包含模拟数据）
└── router/
    └── index.js               # 路由配置（已添加设备管理路由）
```

## 数据库字段映射

根据您提供的SQL表结构，界面包含以下字段：

| 字段名 | 中文名称 | 类型 | 说明 |
|--------|----------|------|------|
| id | 设备ID | int | 主键，自增 |
| device_sn | 设备序列号 | varchar(32) | 设备唯一标识 |
| device_name | 设备名称 | varchar(64) | 设备型号名称 |
| user_id | 用户ID | varchar(64) | 绑定用户账号 |
| nickname | 设备昵称 | varchar(64) | 自定义设备名称 |
| workspace_id | 工作空间ID | varchar(64) | 所属工作空间 |
| device_type | 设备类型 | int | 设备类型编码 |
| sub_type | 子类型 | int | 设备子类型 |
| domain | 域 | int | 设备域 |
| firmware_version | 固件版本 | varchar(32) | 设备固件版本 |
| compatible_status | 兼容状态 | tinyint(1) | 固件版本兼容性 |
| version | 协议版本 | varchar(32) | 通信协议版本 |
| device_index | 设备索引 | varchar(32) | 设备控制索引 |
| child_sn | 子设备序列号 | varchar(32) | 网关控制的子设备 |
| bound_status | 绑定状态 | tinyint(1) | 设备绑定状态 |
| bound_time | 绑定时间 | datetime | 设备绑定时间 |
| login_time | 最后登录 | datetime | 设备最后登录时间 |
| create_time | 创建时间 | datetime | 记录创建时间 |
| update_time | 更新时间 | datetime | 记录更新时间 |
| device_desc | 设备描述 | varchar(100) | 设备描述信息 |
| url_normal | 普通图标 | varchar(200) | 设备普通状态图标 |
| url_select | 选中图标 | varchar(200) | 设备选中状态图标 |

## 使用说明

### 1. 查看演示
访问 `/device/demo` 路径可以查看包含模拟数据的演示页面。

### 2. 集成到项目
1. 确保后端API接口已实现（参考 `src/api/device.js` 中的接口定义）
2. 配置相关字典数据：
   - `device_type`：设备类型字典
   - `device_bound_status`：绑定状态字典
   - `device_compatible_status`：兼容状态字典
3. 在菜单管理中添加设备管理菜单项
4. 配置相应的权限控制

### 3. API接口要求
后端需要实现以下接口：
- `GET /device/list`：获取设备列表（支持分页和筛选）
- `GET /device/{id}`：获取单个设备详情
- `POST /device`：新增设备
- `PUT /device`：修改设备
- `DELETE /device/{id}`：删除设备
- `PUT /device/changeBoundStatus`：修改绑定状态
- `GET /device/export`：导出设备数据

## 技术栈
- Vue 2.6.12
- Element UI 2.15.14
- Vue Router 3.4.9
- Axios 0.28.1

## 注意事项
1. 页面使用了字典数据，需要在系统中配置相应的字典类型
2. 权限控制基于 `v-hasPermi` 指令，需要配置相应的权限标识
3. 导出功能需要后端支持Excel文件生成
4. 时间显示使用了 `parseTime` 全局方法进行格式化

## 扩展建议
1. 可以添加设备状态监控功能
2. 支持设备批量操作
3. 添加设备使用统计图表
4. 支持设备分组管理
5. 添加设备告警功能
