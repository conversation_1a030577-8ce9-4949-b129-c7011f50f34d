/**
 * WebSocket 连接调试工具
 * 用于测试和调试 WebSocket 连接问题
 */

import { getToken } from "@/utils/auth";
import WebSocketConfig from './websocket.js';

class WebSocketDebugger {
  constructor() {
    this.testResults = [];
  }
  
  /**
   * 测试WebSocket连接
   */
  async testConnection() {
    console.log('🔍 开始WebSocket连接测试...');
    this.testResults = [];
    
    const token = getToken();
    console.log('📋 当前Token:', token ? '已获取' : '未获取');
    
    if (!token) {
      this.addResult('❌ 错误', 'Token未获取，请先登录');
      return this.testResults;
    }
    
    // 测试不同的连接方式
    await this.testBasicConnection();
    await this.testTokenInUrl();
    await this.testTokenInHeader();
    await this.testTokenInMessage();
    
    return this.testResults;
  }
  
  /**
   * 测试基础连接
   */
  async testBasicConnection() {
    console.log('🔗 测试基础连接...');
    
    try {
      const wsUrl = WebSocketConfig.getWebSocketUrl('/api/v1/ws');
      console.log('连接地址:', wsUrl);
      
      const result = await this.attemptConnection(wsUrl, '基础连接');
      this.addResult(result.success ? '✅ 成功' : '❌ 失败', `基础连接: ${result.message}`);
      
    } catch (error) {
      this.addResult('❌ 错误', `基础连接异常: ${error.message}`);
    }
  }
  
  /**
   * 测试Token在URL中
   */
  async testTokenInUrl() {
    console.log('🔗 测试Token在URL中...');
    
    try {
      const token = getToken();
      const wsUrl = WebSocketConfig.getAuthenticatedWebSocketUrl('/api/v1/ws', token, 'warning-all');
      console.log('带Token连接地址:', wsUrl);
      
      const result = await this.attemptConnection(wsUrl, 'Token在URL');
      this.addResult(result.success ? '✅ 成功' : '❌ 失败', `Token在URL: ${result.message}`);
      
    } catch (error) {
      this.addResult('❌ 错误', `Token在URL异常: ${error.message}`);
    }
  }
  
  /**
   * 测试Token在Header中（WebSocket不支持自定义Header，但可以尝试）
   */
  async testTokenInHeader() {
    console.log('🔗 测试Token在Header中...');
    this.addResult('⚠️ 跳过', 'WebSocket不支持自定义Header');
  }
  
  /**
   * 测试连接后发送Token
   */
  async testTokenInMessage() {
    console.log('🔗 测试连接后发送Token...');
    
    try {
      const wsUrl = WebSocketConfig.getWebSocketUrl('/api/v1/ws');
      const token = getToken();
      
      const result = await this.attemptConnectionWithAuth(wsUrl, token);
      this.addResult(result.success ? '✅ 成功' : '❌ 失败', `连接后认证: ${result.message}`);
      
    } catch (error) {
      this.addResult('❌ 错误', `连接后认证异常: ${error.message}`);
    }
  }
  
  /**
   * 尝试连接
   */
  attemptConnection(url, type) {
    return new Promise((resolve) => {
      const ws = new WebSocket(url);
      const timeout = setTimeout(() => {
        ws.close();
        resolve({ success: false, message: '连接超时' });
      }, 5000);
      
      ws.onopen = () => {
        clearTimeout(timeout);
        console.log(`${type} 连接成功`);
        ws.close();
        resolve({ success: true, message: '连接成功' });
      };
      
      ws.onerror = (error) => {
        clearTimeout(timeout);
        console.error(`${type} 连接错误:`, error);
        resolve({ success: false, message: '连接错误' });
      };
      
      ws.onclose = (event) => {
        clearTimeout(timeout);
        if (event.code === 1000) {
          // 正常关闭
          return;
        }
        console.log(`${type} 连接关闭:`, event.code, event.reason);
        if (event.code === 403) {
          resolve({ success: false, message: '403 Forbidden - 认证失败' });
        } else {
          resolve({ success: false, message: `连接关闭: ${event.code} ${event.reason}` });
        }
      };
    });
  }
  
  /**
   * 尝试连接并发送认证消息
   */
  attemptConnectionWithAuth(url, token) {
    return new Promise((resolve) => {
      const ws = new WebSocket(url);
      let authSent = false;
      
      const timeout = setTimeout(() => {
        ws.close();
        resolve({ success: false, message: '认证超时' });
      }, 10000);
      
      ws.onopen = () => {
        console.log('连接建立，发送认证消息...');
        
        // 发送认证消息
        const authMessage = {
          type: 'auth',
          token: token,
          username: 'warning-all'
        };
        
        ws.send(JSON.stringify(authMessage));
        authSent = true;
      };
      
      ws.onmessage = (event) => {
        console.log('收到消息:', event.data);
        
        if (event.data === '连接成功' || event.data.includes('成功')) {
          clearTimeout(timeout);
          ws.close();
          resolve({ success: true, message: '认证成功' });
        }
      };
      
      ws.onerror = (error) => {
        clearTimeout(timeout);
        console.error('连接错误:', error);
        resolve({ success: false, message: '连接错误' });
      };
      
      ws.onclose = (event) => {
        clearTimeout(timeout);
        if (event.code === 1000) {
          // 正常关闭
          return;
        }
        
        if (event.code === 403) {
          resolve({ success: false, message: '403 Forbidden - 认证失败' });
        } else if (authSent) {
          resolve({ success: false, message: '认证后连接关闭' });
        } else {
          resolve({ success: false, message: `连接关闭: ${event.code}` });
        }
      };
    });
  }
  
  /**
   * 添加测试结果
   */
  addResult(status, message) {
    const result = {
      status,
      message,
      timestamp: new Date().toLocaleTimeString()
    };
    
    this.testResults.push(result);
    console.log(`${status} ${message}`);
  }
  
  /**
   * 获取连接建议
   */
  getConnectionAdvice() {
    const advice = [
      '🔧 WebSocket连接故障排除建议:',
      '',
      '1. 检查后端WebSocket服务是否启动',
      '2. 确认端口8090是否正确',
      '3. 检查防火墙是否阻止连接',
      '4. 验证Token是否有效',
      '5. 确认后端是否需要特定的认证方式',
      '',
      '📋 常见错误码:',
      '• 403 Forbidden - 认证失败，检查Token',
      '• 404 Not Found - 路径错误，检查WebSocket端点',
      '• 500 Internal Server Error - 服务器内部错误',
      '• 1006 Abnormal Closure - 网络问题或服务器关闭',
      '',
      '💡 建议的认证方式:',
      '1. 在URL中传递Token: ws://host/ws?token=xxx',
      '2. 连接后发送认证消息',
      '3. 使用子协议传递认证信息'
    ];
    
    return advice.join('\n');
  }
}

// 创建全局调试器实例
const wsDebugger = new WebSocketDebugger();

// 导出调试函数
export const debugWebSocket = () => wsDebugger.testConnection();
export const getConnectionAdvice = () => wsDebugger.getConnectionAdvice();

export default WebSocketDebugger;
