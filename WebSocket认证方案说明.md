# WebSocket认证方案说明

## 问题分析

根据您提供的ApiPost调用信息，WebSocket连接需要在请求头中携带`Authorization: Bearer token`。但是，**浏览器中的WebSocket API不支持自定义请求头**，这是一个已知的限制。

## 解决方案

我已经实现了多种WebSocket认证方式，按优先级排序：

### 1. 子协议认证 (推荐)

**原理：** 使用WebSocket子协议模拟HTTP Header的效果

**实现：**
```javascript
const protocols = [
  `authorization.Bearer.${token}`,  // 模拟 Authorization: Bearer token
  `username.${username}`            // 传递用户名
];

const ws = new WebSocket(wsUrl, protocols);
```

**服务器端处理：**
```javascript
// 服务器需要解析子协议
const protocols = request.headers['sec-websocket-protocol'];
// 解析: "authorization.Bearer.eyJhbGciOiJIUzUxMiJ9..."
```

**优点：**
- 最接近HTTP Header方式
- 安全性较高
- 不会在URL中暴露token

**缺点：**
- 需要服务器支持子协议解析

### 2. URL参数认证

**原理：** 在WebSocket URL中传递token参数

**实现：**
```javascript
const wsUrl = `ws://************:8090/api/v1/ws?token=${token}&username=${username}`;
const ws = new WebSocket(wsUrl);
```

**优点：**
- 实现简单
- 兼容性好
- 服务器端容易处理

**缺点：**
- token可能在日志中暴露
- URL长度限制

### 3. 连接后认证

**原理：** 先建立连接，然后发送认证消息

**实现：**
```javascript
const ws = new WebSocket(wsUrl);

ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'auth',
    token: token,
    username: username
  }));
};
```

**优点：**
- 灵活性高
- 可以处理复杂认证流程
- 支持认证失败后的处理

**缺点：**
- 需要额外的消息处理逻辑
- 认证过程较复杂

## 当前实现

### 自动认证系统

创建了 `WebSocketAuth` 类，自动尝试多种认证方式：

```javascript
import WebSocketAuth from '@/utils/websocket-auth.js';

const wsAuth = new WebSocketAuth();

wsAuth.attemptConnection(
  'warning-all',
  (socket, method) => {
    console.log(`连接成功，使用方式: ${method}`);
    // 使用socket
  },
  (error) => {
    console.error('连接失败:', error);
  }
);
```

### 使用方法

在飞控界面中：

```javascript
// 自动尝试最佳认证方式
socketService.init('warning-all', token);
```

系统会按以下顺序尝试：
1. 子协议认证
2. URL参数认证  
3. 连接后认证
4. 基础连接

## 服务器端适配建议

### 方案1：支持子协议认证 (推荐)

```javascript
// Node.js + ws 示例
const WebSocket = require('ws');

const wss = new WebSocket.Server({
  port: 8090,
  path: '/api/v1/ws',
  verifyClient: (info) => {
    const protocols = info.req.headers['sec-websocket-protocol'];
    
    if (protocols) {
      const protocolList = protocols.split(',').map(p => p.trim());
      
      for (const protocol of protocolList) {
        if (protocol.startsWith('authorization.Bearer.')) {
          const token = protocol.replace('authorization.Bearer.', '');
          // 验证token
          if (verifyToken(token)) {
            return true;
          }
        }
      }
    }
    
    return false; // 认证失败
  }
});
```

### 方案2：支持URL参数认证

```javascript
const url = require('url');

const wss = new WebSocket.Server({
  port: 8090,
  path: '/api/v1/ws',
  verifyClient: (info) => {
    const query = url.parse(info.req.url, true).query;
    const token = query.token;
    
    if (token && verifyToken(token)) {
      return true;
    }
    
    return false;
  }
});
```

### 方案3：支持连接后认证

```javascript
wss.on('connection', (ws, req) => {
  let authenticated = false;
  
  // 设置认证超时
  const authTimeout = setTimeout(() => {
    if (!authenticated) {
      ws.close(4001, 'Authentication timeout');
    }
  }, 5000);
  
  ws.on('message', (data) => {
    if (!authenticated) {
      try {
        const message = JSON.parse(data);
        if (message.type === 'auth' && verifyToken(message.token)) {
          authenticated = true;
          clearTimeout(authTimeout);
          ws.send('认证成功');
        } else {
          ws.close(4003, 'Authentication failed');
        }
      } catch (error) {
        ws.close(4002, 'Invalid message format');
      }
    } else {
      // 处理正常消息
      handleMessage(ws, data);
    }
  });
});
```

## 调试工具

### 连接诊断

```javascript
import { debugWebSocket } from '@/utils/websocket-debug.js';

// 运行诊断
debugWebSocket().then(results => {
  results.forEach(result => {
    console.log(`${result.status} ${result.message}`);
  });
});
```

### 手动测试

```javascript
// 测试子协议认证
const ws = new WebSocket('ws://************:8090/api/v1/ws', [
  `authorization.Bearer.${token}`,
  'username.warning-all'
]);

ws.onopen = () => console.log('子协议认证成功');
ws.onerror = () => console.log('子协议认证失败');
```

## 推荐实施步骤

1. **优先实现子协议认证**
   - 修改服务器端支持子协议解析
   - 客户端使用子协议传递token

2. **保留URL参数认证作为备用**
   - 确保向后兼容性
   - 处理不支持子协议的情况

3. **测试和验证**
   - 使用调试工具验证连接
   - 确保所有认证方式都能正常工作

4. **监控和日志**
   - 记录使用的认证方式
   - 监控认证成功率

## 安全考虑

1. **Token保护**
   - 使用HTTPS/WSS协议
   - 定期刷新token
   - 避免在日志中记录完整token

2. **连接验证**
   - 设置认证超时
   - 限制连接频率
   - 验证来源IP

3. **错误处理**
   - 不暴露详细错误信息
   - 记录认证失败尝试
   - 实现熔断机制

通过这套方案，可以在浏览器限制下实现类似HTTP Header认证的效果，确保WebSocket连接的安全性和可靠性。
