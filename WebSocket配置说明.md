# WebSocket 配置说明

## 概述

已将WebSocket地址配置改为通用的，根据环境自动选择合适的连接地址。

## 文件结构

```
src/
├── utils/
│   └── websocket.js          # WebSocket配置工具
└── socket/
    └── index.js              # WebSocket服务
```

## 配置说明

### 1. WebSocket配置工具 (`src/utils/websocket.js`)

提供了根据环境自动配置WebSocket地址的工具：

- **开发环境**：使用 `ws://127.0.0.1:8090`（对应vue.config.js中的代理配置）
- **生产环境**：使用当前域名，自动判断协议（http/https对应ws/wss）

### 2. WebSocket服务 (`socket/index.js`)

增强的WebSocket服务，包含以下特性：

#### 核心功能
- ✅ **自动重连**：连接断开时自动重连（最多5次）
- ✅ **消息处理**：支持多个消息处理器
- ✅ **错误处理**：完善的错误处理和日志
- ✅ **状态检查**：提供连接状态检查方法
- ✅ **兼容性**：保持与旧版本API的兼容

#### 新增方法
```javascript
// 添加消息处理器
socketService.addMessageHandler(callback);

// 移除消息处理器
socketService.removeMessageHandler(callback);

// 检查连接状态
socketService.isConnected();

// 获取连接状态码
socketService.getReadyState();
```

## 使用方法

### 基本使用

```javascript
import socketService from '@/socket/index.js';

// 初始化连接
socketService.init('username');

// 添加消息处理器
socketService.addMessageHandler((data) => {
  console.log('收到消息:', data);
});

// 发送消息
socketService.send({
  type: 'message',
  content: 'Hello WebSocket'
});

// 关闭连接
socketService.close();
```

### 在Vue组件中使用

```javascript
export default {
  mounted() {
    // 初始化WebSocket连接
    this.$socketService.init(this.$store.state.user.name);
    
    // 添加消息处理器
    this.$socketService.addMessageHandler(this.handleMessage);
  },
  
  beforeDestroy() {
    // 移除消息处理器
    this.$socketService.removeMessageHandler(this.handleMessage);
    
    // 关闭连接
    this.$socketService.close();
  },
  
  methods: {
    handleMessage(data) {
      // 处理接收到的消息
      console.log('收到WebSocket消息:', data);
    },
    
    sendMessage() {
      if (this.$socketService.isConnected()) {
        this.$socketService.send({
          type: 'chat',
          message: 'Hello'
        });
      }
    }
  }
}
```

### 兼容旧版本

```javascript
// 旧版本的使用方式仍然支持
socketService.getMessage().then(data => {
  console.log('收到消息:', data);
});
```

## 环境配置

### 开发环境

确保 `vue.config.js` 中的代理配置正确：

```javascript
devServer: {
  proxy: {
    [process.env.VUE_APP_BASE_API]: {
      target: 'http://127.0.0.1:8090',
      changeOrigin: true,
      pathRewrite: {
        ['^' + process.env.VUE_APP_BASE_API]: ''
      }
    }
  }
}
```

### 生产环境

生产环境会自动使用当前域名，无需额外配置。

## 连接地址规则

| 环境 | 协议 | 地址 | 示例 |
|------|------|------|------|
| 开发环境 | ws | 127.0.0.1:8090 | `ws://127.0.0.1:8090/api/v1/ws` |
| 生产环境(HTTP) | ws | 当前域名 | `ws://yourdomain.com/api/v1/ws` |
| 生产环境(HTTPS) | wss | 当前域名 | `wss://yourdomain.com/api/v1/ws` |

## 重连机制

- **重连次数**：最多5次
- **重连间隔**：3秒
- **触发条件**：连接错误或非正常关闭

## 注意事项

1. **消息格式**：自动处理JSON格式的消息
2. **连接状态**：发送消息前建议检查连接状态
3. **内存泄漏**：组件销毁时记得移除消息处理器
4. **错误处理**：所有错误都会在控制台输出日志

## 故障排除

### 常见问题

1. **连接失败**
   - 检查后端WebSocket服务是否启动
   - 确认端口号是否正确
   - 检查防火墙设置

2. **重连失败**
   - 检查网络连接
   - 确认后端服务稳定性
   - 查看控制台错误日志

3. **消息发送失败**
   - 使用 `isConnected()` 检查连接状态
   - 确认消息格式正确
   - 检查后端消息处理逻辑

### 调试方法

```javascript
// 检查连接状态
console.log('WebSocket状态:', socketService.getReadyState());

// 检查是否已连接
console.log('是否已连接:', socketService.isConnected());

// 查看重连次数
console.log('重连次数:', socketService.reconnectAttempts);
```
