/**
 * WebRTC 播放器工具类
 * 用于处理 WebRTC 视频流播放
 */

class WebRTCPlayer {
  constructor(options = {}) {
    this.videoElement = null
    this.peerConnection = null
    this.websocket = null
    this.signalingUrl = options.signalingUrl || 'ws://localhost:8080/signaling'
    this.stunServers = options.stunServers || [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' }
    ]
    this.turnServers = options.turnServers || []
    
    this.onConnected = options.onConnected || (() => {})
    this.onDisconnected = options.onDisconnected || (() => {})
    this.onError = options.onError || (() => {})
    this.onStreamReceived = options.onStreamReceived || (() => {})
    
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 3000
  }
  
  /**
   * 初始化 WebRTC 连接
   * @param {HTMLVideoElement} videoElement - 视频元素
   * @param {string} streamId - 流ID
   */
  async init(videoElement, streamId) {
    this.videoElement = videoElement
    this.streamId = streamId
    
    try {
      await this.createPeerConnection()
      await this.connectSignaling()
      await this.startNegotiation()
    } catch (error) {
      console.error('WebRTC初始化失败:', error)
      this.onError(error)
    }
  }
  
  /**
   * 创建 PeerConnection
   */
  async createPeerConnection() {
    const config = {
      iceServers: [...this.stunServers, ...this.turnServers],
      iceCandidatePoolSize: 10
    }
    
    this.peerConnection = new RTCPeerConnection(config)
    
    // 监听 ICE 候选
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendSignalingMessage({
          type: 'ice-candidate',
          candidate: event.candidate,
          streamId: this.streamId
        })
      }
    }
    
    // 监听连接状态变化
    this.peerConnection.onconnectionstatechange = () => {
      console.log('WebRTC连接状态:', this.peerConnection.connectionState)
      
      switch (this.peerConnection.connectionState) {
        case 'connected':
          this.isConnected = true
          this.reconnectAttempts = 0
          this.onConnected()
          break
        case 'disconnected':
        case 'failed':
          this.isConnected = false
          this.onDisconnected()
          this.handleReconnect()
          break
        case 'closed':
          this.isConnected = false
          this.onDisconnected()
          break
      }
    }
    
    // 监听远程流
    this.peerConnection.ontrack = (event) => {
      console.log('收到远程流:', event.streams[0])
      if (this.videoElement) {
        this.videoElement.srcObject = event.streams[0]
        this.onStreamReceived(event.streams[0])
      }
    }
  }
  
  /**
   * 连接信令服务器
   */
  async connectSignaling() {
    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket(this.signalingUrl)
        
        this.websocket.onopen = () => {
          console.log('信令服务器连接成功')
          resolve()
        }
        
        this.websocket.onmessage = (event) => {
          this.handleSignalingMessage(JSON.parse(event.data))
        }
        
        this.websocket.onerror = (error) => {
          console.error('信令服务器连接错误:', error)
          reject(error)
        }
        
        this.websocket.onclose = () => {
          console.log('信令服务器连接关闭')
          if (this.isConnected) {
            this.handleReconnect()
          }
        }
        
      } catch (error) {
        reject(error)
      }
    })
  }
  
  /**
   * 发送信令消息
   */
  sendSignalingMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message))
    }
  }
  
  /**
   * 处理信令消息
   */
  async handleSignalingMessage(message) {
    try {
      switch (message.type) {
        case 'offer':
          await this.handleOffer(message.offer)
          break
        case 'answer':
          await this.handleAnswer(message.answer)
          break
        case 'ice-candidate':
          await this.handleIceCandidate(message.candidate)
          break
        case 'error':
          this.onError(new Error(message.error))
          break
        default:
          console.warn('未知信令消息类型:', message.type)
      }
    } catch (error) {
      console.error('处理信令消息失败:', error)
      this.onError(error)
    }
  }
  
  /**
   * 开始协商
   */
  async startNegotiation() {
    // 请求观看流
    this.sendSignalingMessage({
      type: 'watch',
      streamId: this.streamId
    })
  }
  
  /**
   * 处理 Offer
   */
  async handleOffer(offer) {
    await this.peerConnection.setRemoteDescription(offer)
    const answer = await this.peerConnection.createAnswer()
    await this.peerConnection.setLocalDescription(answer)
    
    this.sendSignalingMessage({
      type: 'answer',
      answer: answer,
      streamId: this.streamId
    })
  }
  
  /**
   * 处理 Answer
   */
  async handleAnswer(answer) {
    await this.peerConnection.setRemoteDescription(answer)
  }
  
  /**
   * 处理 ICE 候选
   */
  async handleIceCandidate(candidate) {
    await this.peerConnection.addIceCandidate(candidate)
  }
  
  /**
   * 处理重连
   */
  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`WebRTC重连中... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.reconnect()
      }, this.reconnectDelay)
    } else {
      console.error('WebRTC重连失败，已达到最大重连次数')
      this.onError(new Error('连接失败，请检查网络'))
    }
  }
  
  /**
   * 重连
   */
  async reconnect() {
    try {
      this.destroy()
      await this.init(this.videoElement, this.streamId)
    } catch (error) {
      console.error('WebRTC重连失败:', error)
      this.handleReconnect()
    }
  }
  
  /**
   * 获取连接统计信息
   */
  async getStats() {
    if (!this.peerConnection) return null
    
    const stats = await this.peerConnection.getStats()
    const result = {
      video: {},
      audio: {},
      connection: {}
    }
    
    stats.forEach((report) => {
      if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
        result.video = {
          bytesReceived: report.bytesReceived,
          packetsReceived: report.packetsReceived,
          packetsLost: report.packetsLost,
          framesDecoded: report.framesDecoded,
          frameWidth: report.frameWidth,
          frameHeight: report.frameHeight,
          framesPerSecond: report.framesPerSecond
        }
      } else if (report.type === 'candidate-pair' && report.state === 'succeeded') {
        result.connection = {
          currentRoundTripTime: report.currentRoundTripTime,
          availableOutgoingBitrate: report.availableOutgoingBitrate,
          availableIncomingBitrate: report.availableIncomingBitrate
        }
      }
    })
    
    return result
  }
  
  /**
   * 销毁连接
   */
  destroy() {
    if (this.peerConnection) {
      this.peerConnection.close()
      this.peerConnection = null
    }
    
    if (this.websocket) {
      this.websocket.close()
      this.websocket = null
    }
    
    if (this.videoElement) {
      this.videoElement.srcObject = null
    }
    
    this.isConnected = false
  }
}

export default WebRTCPlayer
